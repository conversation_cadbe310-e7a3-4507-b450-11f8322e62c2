# 智慧停车平台车场运营管理后台（B端）完整功能清单

## 1. 车场信息

**功能描述**：管理停车场基础信息、物理设施及空间划分，支持路内路外不同类型车场的统一管理，提供车场快速建设和配置功能。系统支持多层级车场管理，包括场中场嵌套结构，提供完整的车场生命周期管理功能

### 子功能

- 车场管理：维护车场名称、地址、经纬度、区域编码等基础信息，支持车场类型区分（路外室内/室外/路内），提供车场编号自动生成和重名校验。支持车场图片上传、拼音全拼和首字母索引生成，提供车场父子关系管理（场中场）。支持针对车场的相关运营配置（如：自动开闸设置：临停车识别自动开杆进场/出场开关控制、时段限制：临停车时段限制开关及限制时间范围设置（开始时间/结束时间）、无牌车管理：无牌车车牌输入功能开关、无进场记录直接出场开关、车牌识别：支持模糊匹配车牌功能、地杆二次进场校验开关、实时计算车位开关等）。包含车场导出功能和物业端车场分页查询
- 通道管理：配置进出口通道，支持通道类型设置（进场/出场）和通道编号管理，关联用户权限控制。提供通道设备绑定、通道状态监控，支持地感确认和通道过车记录。包含用户通道权限分配和岗亭端通道管理功能
- 车位管理：车位分区编号及状态监控，支持车位占用状态实时更新（空闲/占用），提供车位运行状况查询和车位显示屏管理。支持车位设备序列号绑定、车位缓存管理，提供车位级别的营收统计和车位使用率分析。包含车场楼层管理和区域划分功能
- 停车设备：管理道闸、摄像头等硬件设备，支持设备序列号绑定和设备状态监控。提供设备在线状态检测、设备故障报警，支持设备参数配置和远程控制。包含地锁设备管理、视频桩设备管理和设备日志校准功能

## 2. 收费规则

**功能描述**：配置差异化收费策略及第三方合作规则，支持分时段、分车型的灵活计费模式，提供优先计费、分段计费、封顶计费等多种计费策略。系统支持复杂的计费规则配置，包括免费时长、分段递增、日封顶等多维度计费控制

### 子功能

- 优惠配置：支持新能源车辆、对接外部平台（洗车、充电、金融机构等第三方平台）优惠。提供优惠规则配置、优惠使用统计分析。支持多种优惠类型（立减、折扣、时长）和优惠叠加规则配置
- 临停收费规则：设置分时段计价标准，支持优先计费时长（如首小时优惠）、分段收费配置（如超时递增计费）、日封顶金额设置，提供车型差异化计费（小车/大车不同费率）。包含工作日/节假日差异化计费、时段计费规则和特殊时间段免费设置
- 月卡收费规则：长期套餐定价策略，支持不同车场的月卡规则配置和套餐管理。提供月卡类型管理（限时/包月/包年）、月卡价格配置、月卡车位数量限制。支持月卡续费规则。

## 3. 车辆授权

**功能描述**：管理各类车辆的准入权限，支持多种车辆类型的授权管理和权限控制，提供车辆进出场权限验证和授权状态管理。系统采用责任链模式处理车辆进出场权限验证，支持复杂的权限判断逻辑和优先级控制

### 子功能

- 月卡车管理：月卡车辆信息维护，支持审批流程和开卡管理，提供月卡状态跟踪（待审核/已生效/已过期/已停用）。包含月卡车辆绑定、月卡有效期管理、月卡使用记录统计。支持一户多车管理和月卡车辆转移功能，提供月卡车辆黑白名单管理
- 访客车授权：临时访客车辆授权，支持授权时间设置（开始时间/结束时间）和权限范围控制（指定车场/全部车场）。提供访客车预约管理、访客车进出记录跟踪、访客车授权状态实时更新。支持批量访客车授权和访客车授权模板管理
- 免费车管理：免费通行车辆管理，支持免费车授权和车场关联设置。提供免费车类型管理（政府车辆/应急车辆/内部车辆），支持免费车有效期设置和免费车使用统计。包含免费车授权审批流程和免费车权限范围控制
- 黑名单管理：禁止通行车辆管理，提供黑名单车辆拦截功能。支持黑名单原因记录、黑名单有效期设置、黑名单车辆进出场拦截日志。包含黑名单车辆自动识别、黑名单车辆报警通知和黑名单车辆解除管理功能
- 黄名单管理：重点关注车辆管理，支持特殊标记和监控。提供黄名单车辆进出场记录跟踪、黄名单车辆行为分析、黄名单车辆风险评估。支持黄名单车辆自动转黑名单规则配置
- 特殊车管理：特殊类型车辆管理，如军用、警用、应急车辆等。支持特殊车辆类型分类、特殊车辆权限配置、特殊车辆通行规则设置。提供特殊车辆识别标识和特殊车辆通行记录统计
- 员工车管理：内部员工车辆管理，支持员工车辆录入，可指定差异化的计费规则。
- 违法车管理：违法违规车辆管理，提供违法车辆记录。可对接违法车辆管理相关管理政府机构进行数据同步。
- 一户多车查询：支持一个月卡车主多辆车（月卡车）的关联查询。

## 4. 停车记录

**功能描述**：全量记录车辆进出数据，支持路内路外停车记录的统一管理，提供车辆识别记录查询和停车状态实时监控。系统采用分布式锁机制确保停车记录的准确性，支持频繁进出合并处理和异常停车记录修复

### 子功能

- 路内停车记录：路内停车位的车辆进出记录，支持车位锁触发和频繁进出合并处理（配置时间间隔内的多次进出自动合并）。提供路内停车计费、路内停车状态监控、路内停车异常处理。包含车位锁状态同步、停车图片保存和停车事件消息推送功能
- 路外停车记录：路外停车场的车辆进出记录，支持通道识别和停车订单关联。提供进场确认机制（地感确认）、出场计费处理、停车时长统计。支持重复进场处理、停车记录修正和停车状态实时更新
- 在场车辆管理：当前在场车辆状态管理，提供实时在场车辆查询和停车时长统计。支持在场车辆列表展示、在场时长计算、在场车辆费用预估。包含在场车辆异常检测、长时间停车提醒和在场车辆统计报表
- 识别记录查询：车牌识别记录查询，支持识别准确率统计和异常识别处理。提供识别图片查看、识别结果校正、识别错误分析。包含识别设备性能监控、识别算法优化建议和识别准确率提升方案
- 第三方记录查询：支持第三方平台（指充电、洗车平台等）推送记录查询功能。

## 5. 社区管理

**功能描述**：住宅社区关联车辆管理，支持社区业主车辆信息登记和亲情卡管理。系统提供社区基础管理功能，支持业主车辆权限管理

### 子功能

- 亲情卡信息：亲情卡车辆授权管理，支持家庭成员车辆关联。提供亲情卡申请流程、亲情卡使用权限设置、亲情卡有效期管理。支持亲情卡车辆进出场记录跟踪和亲情卡使用统计分析
- 社区基础信息维护：社区基本信息管理，包括社区名称、地址、联系方式等。支持楼栋、组团、房号的分组维护。提供社区车场关联管理。
- 业主信息登记：支持业主基础信息录入和维护，支持批量信息录入和批量导入功能。支持在录入月卡时关联业主信息，提供以业主为主体视角的月卡车辆的查询功能。

## 6. 订单中心

**功能描述**：全渠道支付订单管理，支持多种支付方式的订单处理，提供订单状态跟踪和财务对账功能。系统采用统一订单处理架构，支持订单全生命周期管理和复杂的订单业务逻辑处理

### 子功能

- 临停支付订单：临时停车费用订单管理，支持实时计费和订单生成。提供订单创建、订单金额计算、订单支付处理、订单状态更新。支持订单异常处理、订单重复支付检测、订单退款处理。包含订单支付超时处理和订单催缴功能
- 月卡支付订单：月卡购买和续费订单管理，支持套餐订单处理。提供月卡订单创建、月卡价格计算、月卡支付确认、月卡生效处理。支持月卡订单发票开具
- 提前缴费订单：提供提前缴费订单（场内缴费）的查询功能
- 月卡欠费订单：月卡欠费订单跟踪，支持欠费提醒和催缴。提供欠费订单自动生成、欠费金额计算、欠费通知发送、欠费处理跟踪。支持欠费减免申请处理
- 退款订单处理：退款申请和处理流程，支持退款审核和资金退回。提供退款申请提交、退款原因审核、退款金额确认、退款资金处理。支持部分退款、全额退款和退款状态跟踪
- 财务对账报表：订单财务数据统计和对账报表生成。提供日对账、月对账、年对账报表，支持收入统计、支出统计、利润分析。包含财务数据导出和财务异常预警功能

## 7. 欠费管理

**功能描述**：未支付订单追踪催缴，支持欠费车辆管理和欠费统计分析。系统提供智能化欠费管理功能，支持欠费预警、自动催缴和欠费处理全流程管理

### 子功能

- 追缴统计报表：欠费数据统计和追缴效果分析。提供欠费金额统计、欠费车辆数量统计、追缴成功率分析、追缴时效分析。支持欠费趋势预测和追缴策略优化建议
- 欠费车辆清单：欠费车辆列表管理，支持欠费金额和时长统计。提供欠费车辆详细信息、欠费历史记录、欠费处理状态。支持欠费车辆分类管理和欠费车辆批量处理
- 历史欠费记录查询：历史欠费记录查询和分析，支持欠费趋势统计。提供欠费记录检索、欠费数据分析、欠费规律发现。支持欠费数据导出和欠费报告生成
- 跨区域追缴：跨区域追缴功能，通过分账功能支持跨区域追缴处理。提供跨区域追缴申请、跨区域追缴处理、跨区域追缴结果跟踪。支持跨区域追缴数据统计和跨区域追缴效果分析

## 8. 稽核管理

**功能描述**：异常通行事件审核，支持无牌车和异常放行的稽核管理，提供稽核审批流程。系统建立完善的稽核体系，确保停车场运营的规范性和安全性

- 异常放行稽核：

### 子功能

- 异常放行稽核：提供对系统记录的非正常放行车辆进行核查（如无牌车放行、特殊放行等）

## 9. 会员中心

**功能描述**：用户会员体系运营，支持会员信息管理和会员行为分析。系统提供完整的会员运营体系，支持会员等级管理、会员权益配置和会员营销活动

### 子功能

- 会员行为分析：会员停车行为数据分析和统计。提供会员停车频次分析、会员消费习惯分析、会员活跃度统计、会员流失预警。支持会员行为轨迹跟踪和会员价值评估
- 会员标签管理：会员标签分类和管理，支持个性化服务。提供会员标签创建、会员标签分配、会员标签统计、会员标签应用。支持基于标签的精准营销和个性化推荐服务
- 会员信息维护：会员基础信息管理和更新。提供会员档案管理、会员信息验证、会员权益管理。会员数据安全保护

## 10. 支付管理

**功能描述**：支付渠道配置，支持多种支付方式接入和支付参数管理，提供统一支付接口和支付结果处理。系统采用统一支付网关架构，支持多渠道支付接入和支付安全管控

### 子功能

- 商户账户管理：支付商户账户信息管理，支持多商户配置。提供商户资质审核、商户费率配置、商户结算管理、商户风险控制。支持商户账户状态监控和商户交易数据统计分析
- 支付参数配置：支付渠道参数配置，支持微信、支付宝、银行卡、ETC等多种支付方式。提供支付接口配置、支付安全设置、支付限额管理、支付手续费配置。支持支付渠道切换和支付成功率监控

## 11. 商户管理

**功能描述**：车场商户管理，支持商户信息维护和商户套餐管理，提供商户授权车辆管理和多维度报表分析。系统支持B2B商户服务模式，提供完整的商户运营管理功能

### 子功能

- 商户信息管理：商户基础信息维护，支持商户用户关联和权限管理。提供商户注册审核、商户资质管理、商户联系人管理、商户状态控制。支持商户层级管理和商户数据权限隔离
- 商户套餐管理：商户套餐配置和管理，支持不同套餐类型设置（时长套餐/余额套餐/计次套餐）。提供套餐价格配置、套餐权益设置、套餐有效期管理、套餐使用统计。
- 商户授权车辆：商户授权车辆管理，支持授权时长和使用记录跟踪。提供车辆授权申请、车辆权限审核、车辆使用监控、车辆权限回收。
- 多维报表分析：根据套餐类型的不同，生成不同的统计报表，支持商户经营数据分析。提供商户收入统计、商户车辆使用分析。

## 12. 运营中心

**功能描述**：日常运营监控，提供全面的运营管理工具，支持巡检任务管理、车场人员排班和多维度经营报表分析。系统建立完整的运营管理体系，支持运营数据实时监控和运营效率优化

### 子功能

- 巡检管理：完整的巡检管理体系，确保停车场设施正常运行
  - 巡检项管理：巡检项目配置和维护，支持巡检项目分类、巡检标准设置
  - 巡检现象管理：巡检发现问题的现象分类管理，提供现象描述模板、现象等级划分、现象处理建议
  - 巡检异常原因管理：异常原因分类和处理方案管理，支持原因分析模板、处理流程配置、责任人分配
  - 巡检计划管理：巡检计划制定和调度安排，提供计划模板、计划执行监控、计划调整功能
  - 巡检任务管理：巡检任务分配和执行跟踪，支持任务派发、任务状态跟踪、任务完成验收
  - 巡检记录：巡检结果记录和历史数据查询，提供巡检报告生成、巡检数据统计、巡检效果分析
  - 巡检分析：巡检结果分析，提供巡检效果评估、巡检效果预测、巡检效果优化建议
- 人员排班管理：工作人员排班计划和班次管理，支持班次模板配置、排班冲突检测、排班统计分析
- 停车经营报表：停车场经营数据统计和分析报表，提供收入分析、成本分析、利润分析、经营趋势预测
- 停车收益报表：停车收益数据统计和趋势分析，支持日收益、月收益、年收益统计，提供收益对比分析和收益优化建议
- 车位营收报表：车位级别的营收数据统计和分析，支持车位使用率分析、车位收益排名、车位效益评估

## 13. 运维中心

**功能描述**：系统运维保障，提供系统运维工具和配置管理，支持第三方集成和系统监控。系统建立完善的运维管理体系，确保系统稳定运行和数据安全

### 子功能

- 第三方商户：第三方商户接入和管理，支持商户资质审核、接口权限配置、数据同步管理、服务质量监控
- 用户协议管理：用户服务协议配置和版本管理，提供协议模板管理、协议版本控制、协议生效管理、协议合规检查
- 开闸放行记录：手动开闸放行操作记录和审计，支持放行原因记录、操作人员追踪、放行统计分析、异常放行预警
- 地锁日志校准：地锁设备日志校准和同步，提供设备时间同步、日志数据校验、设备状态监控、故障诊断功能
- 二维码导出：停车二维码批量导出功能，支持二维码批量生成、二维码样式配置、二维码数据管理、二维码使用统计
- 二维码模板管理：二维码样式模板配置和管理，支持模板自定义设计、模板效果预览、模板应用统计、模板优化建议
- 小票打印模板：路边停车小票打印模板配置和管理，提供模板设计工具、模板预览功能、模板版本管理、打印效果优化
- 特殊放行原因：特殊放行原因维护，用于路外停车场景下免费放行时的放行原因选项
- 检测准确率报表：车牌识别准确率统计和分析报表，提供识别成功率统计、识别错误分析等。为车场运营方提供车牌识别优化建议和识别算法改进方向
- 车场楼层管理：多层停车场楼层信息管理，提供楼层结构配置、楼层导航设置、楼层车位分布、楼层使用统计
- 车场区域管理：停车场区域划分和管理，支持区域功能定义、区域权限设置、区域使用监控、区域效益分析
- 车位显示屏管理：车位显示屏设备管理和内容配置，提供显示内容配置、显示效果预览、设备状态监控、内容更新管理

## 14. 审批中心

**功能描述**：业务流程审批，提供完整的审批流程管理，支持月卡开卡、异常稽核等业务审批流程。系统采用工作流引擎，支持复杂的审批流程配置和审批状态跟踪

### 子功能

- 待我审批：当前用户待处理的审批任务列表，提供任务优先级排序、任务详情查看、批量审批处理、审批时限提醒
- 我发起的：当前用户发起的审批申请跟踪，支持申请状态查询、申请进度跟踪、申请撤回功能、申请结果通知
- 抄送我的：抄送给当前用户的审批信息，提供抄送消息查看、抄送统计分析、抄送规则配置、抄送提醒设置
- 我审批的：当前用户已处理的审批记录，支持审批历史查询、审批决策统计、审批效率分析、审批质量评估
- 审批配置：审批流程配置和审批规则设置，提供流程设计器、审批节点配置、审批条件设置、审批权限管理

## 15. 财务中心

**功能描述**：资金账务管理，提供完整的财务管理功能，支持账单管理、对账结算和开票服务。系统建立完善的财务管理体系，确保资金安全和财务合规

### 子功能

- 账单流水：详细的财务流水记录和查询，提供流水明细查询、流水分类统计、流水对账功能、流水异常检测
- 停车对账单：停车费用对账单生成和管理，支持日对账、月对账、年对账，提供对账差异分析、对账结果确认、对账报告生成
- 停车结算单：停车费用结算单处理和分账管理，提供结算规则配置、分账比例设置、结算周期管理、结算状态跟踪
- 开票记录查询：发票开具记录查询和管理，提供发票申请处理、发票状态跟踪、发票信息验证、发票统计分析

## 16. 系统管理

**功能描述**：系统基础配置，提供系统基础数据管理和配置功能，支持用户权限管理和系统参数配置。系统采用多租户架构，支持租户隔离和统一管理

### 子功能

- 用户管理：系统用户账户管理，支持用户创建、权限分配和状态管理。提供用户信息维护、用户密码管理、用户登录控制、用户操作审计。支持用户批量导入导出、用户组织关系管理、用户角色分配
- 行政区管理：行政区域数据管理和维护，提供省市区县四级行政区划数据，支持行政区编码管理、行政区层级关系维护、行政区数据同步更新
- 机构管理：组织机构信息管理和层级关系维护，支持机构树形结构管理、机构人员分配、机构权限设置、机构数据统计。提供机构合并拆分、机构变更审批功能
- 岗位管理：岗位信息配置和职责定义，支持岗位层级管理、岗位职责描述、岗位权限配置、岗位人员分配。提供岗位绩效考核和岗位调整管理
- 系统字典：系统基础字典数据管理，提供字典分类管理、字典项维护、字典缓存管理、字典数据同步。支持字典数据导入导出和字典使用统计
- 业务字典：业务相关字典数据配置，支持业务场景字典定义、字典业务规则配置、字典数据验证、字典变更影响分析
- 菜单管理：系统菜单配置和权限关联，支持菜单树形结构管理、菜单图标配置、菜单路由管理、菜单权限控制。提供菜单国际化和菜单个性化定制
- 参数管理：系统参数配置和动态参数管理，支持参数分类管理、参数值验证、参数变更审批、参数使用监控。提供参数备份恢复和参数版本管理
- 应用管理：应用系统管理和配置，用于管理系统应用和集成服务，提供应用接入管理、应用权限管理。

## 17. 权限管理

**功能描述**：细粒度权限控制，提供基于角色的权限管理体系，支持菜单权限、数据权限和接口权限的统一管理。系统采用RBAC权限模型，支持复杂的权限继承和权限委托

### 子功能

- 角色管理：角色定义和权限分配，支持角色继承和权限授权。提供角色模板管理、角色权限矩阵、角色使用统计、角色权限审计。支持角色动态调整和角色权限回收
- 数据权限：数据访问权限控制，支持数据范围权限设置（全部数据/本部门数据/本人数据/自定义数据）。提供数据权限规则配置、数据权限验证、数据权限审计
- 接口权限：API接口访问权限控制，支持接口级别的权限管理。提供接口权限配置、接口调用监控、接口权限审计、接口安全防护

## 18. 开放平台

**功能描述**：第三方接入管理，提供开放API接口和第三方应用接入管理。系统支持标准化的API接口，提供完整的第三方集成解决方案

### 子功能

- 接口管理：开放API接口管理和文档维护，提供接口版本管理、接口文档生成、接口测试工具、接口性能监控。支持接口限流控制和接口安全认证
- 应用管理：第三方应用接入管理和权限控制，提供应用注册审核、应用密钥管理、应用调用统计、应用权限配置。支持应用沙箱测试和应用上线审批

## 19. 资源管理

**功能描述**：基础资源服务，提供系统基础服务和资源管理功能。系统提供统一的资源管理平台，支持多种资源类型的统一管理和监控

### 子功能

- 对象存储：文件存储服务管理和配置，提供文件上传下载、文件分类管理、文件权限控制、文件备份恢复。支持多种存储方式（本地存储/云存储）和文件CDN加速
- 短信配置：短信服务配置和模板管理，提供短信通道配置、短信模板管理、短信发送统计、短信费用监控。支持多短信服务商接入和短信发送失败重试
- 任务调度：定时任务调度管理，支持任务配置和执行监控。提供任务调度策略、任务执行日志、任务失败处理、任务性能监控。支持分布式任务调度和任务负载均衡

## 20. 日志管理

**功能描述**：日志分析

### 子功能

- 登录日志：
- 日志分析审计：系统日志分析和审计功能，支持操作日志跟踪和安全审计。提供日志收集、日志分析、日志检索、日志报警。支持日志数据可视化和日志异常检测

## 21. 活动管理

**功能描述**：营销活动运营，提供完整的营销活动管理功能，支持优惠券发放、推广活动和增值服务管理。系统建立完整的营销活动体系，支持精准营销和效果评估

### 子功能

- 优惠券：优惠券活动管理，支持优惠券创建、发放规则配置和使用统计。提供优惠券模板管理、优惠券批量发放、优惠券使用限制、优惠券核销管理。支持优惠券活动效果分析和优惠券ROI计算
- 优惠券明细：优惠券使用明细查询和统计分析，提供优惠券使用记录、优惠券核销统计、优惠券用户分析、优惠券商户分析。支持优惠券数据导出和优惠券报表生成
- 推广工具（含2个子项）：营销推广工具集，提供多渠道推广支持
  - 推广渠道：推广渠道管理和效果统计，支持渠道注册、渠道权限管理、渠道数据统计、渠道佣金结算
  - 渠码推广：渠道二维码推广和数据追踪，提供二维码生成、扫码统计、转化分析、推广效果评估
- 洗车卡服务：洗车服务管理，支持洗车站点管理和洗车卡服务配置。提供洗车站点注册、洗车服务配置、洗车订单管理、洗车服务统计。支持洗车站点地理位置管理和洗车服务质量评估
